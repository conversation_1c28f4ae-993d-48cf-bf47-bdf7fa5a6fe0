#!/usr/bin/env node

/**
 * Test script for chat regeneration functionality
 * This script tests the complete regeneration flow:
 * 1. Send a chat message
 * 2. Get the message ID
 * 3. Regenerate the response
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5529/api';

async function testRegeneration() {
  console.log('🧪 Testing Chat Regeneration Functionality\n');

  try {
    // Step 1: Send a simple chat message first
    console.log('1️⃣ Sending initial chat message...');
    
    const chatResponse = await fetch(`${BASE_URL}/chat/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Tell me a short joke about programming.',
        llmModel: 'gpt-3.5-turbo'
      })
    });

    if (!chatResponse.ok) {
      throw new Error(`Chat request failed: ${chatResponse.status} ${chatResponse.statusText}`);
    }

    const chatData = await chatResponse.json();
    console.log('✅ Chat message sent successfully');
    console.log(`   Message ID: ${chatData.data.messageId}`);
    console.log(`   Response: ${chatData.data.response.substring(0, 100)}...`);

    // Step 2: Test regeneration with the message ID
    console.log('\n2️⃣ Testing regeneration...');
    
    const regenerateResponse = await fetch(`${BASE_URL}/chat/regenerate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messageId: chatData.data.messageId,
        llmModel: 'gpt-3.5-turbo'
      })
    });

    if (!regenerateResponse.ok) {
      const errorText = await regenerateResponse.text();
      throw new Error(`Regeneration request failed: ${regenerateResponse.status} ${regenerateResponse.statusText}\n${errorText}`);
    }

    // Since regeneration uses streaming, we need to handle the SSE response
    console.log('✅ Regeneration request accepted');
    console.log('📡 Receiving streaming response...');

    const reader = regenerateResponse.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            
            switch (data.type) {
              case 'start':
                console.log('🚀 Stream started');
                break;
              case 'chunk':
                fullResponse += data.content;
                process.stdout.write('.');
                break;
              case 'complete':
                console.log('\n✅ Regeneration completed successfully!');
                console.log(`   New response: ${data.fullResponse || fullResponse}`);
                console.log(`   Message ID: ${data.metadata?.messageId || 'N/A'}`);
                console.log(`   Version: ${data.metadata?.version || 'N/A'}`);
                return;
              case 'error':
                throw new Error(`Streaming error: ${data.error}`);
            }
          } catch (parseError) {
            console.warn('Failed to parse SSE data:', parseError.message);
          }
        }
      }
    }

    console.log('\n✅ Regeneration test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testRegeneration().then(() => {
  console.log('\n🎉 All tests passed!');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test suite failed:', error.message);
  process.exit(1);
});
